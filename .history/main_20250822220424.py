# Import required libraries
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from starlette.websockets import WebSocket, WebSocketDisconnect
import html
import logging

# Create the FastAPI app
app = FastAPI()

# Mount the static files directory
app.mount("/static", StaticFiles(directory="static"), name="static")

# Create a Jinja2 templates object
templates = Jinja2Templates(directory="templates")

# Define the WebSocket connection manager class
class ConnectionManager:
    def __init__(self):
        self.active_connections = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)

    def disconnect(self, websocket: WebSocket):
        try:
            self.active_connections.remove(websocket)
        except ValueError:
            logging.warning("Attempted to disconnect websocket that was not in active connections")

    async def broadcast(self, message: str):
        # Sanitize the message
        sanitized_message = html.escape(message).strip()
        
        # Validate message length
        if len(sanitized_message) > 1000:
            sanitized_message = sanitized_message[:1000] + "..."
        
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(sanitized_message)
            except Exception as e:
                logging.error(f"Error sending message to websocket: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected connections
        for conn in disconnected:
            self.disconnect(conn)

# Instantiate the WebSocket connection manager
manager = ConnectionManager()

# Define the WebSocket endpoint
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    await manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            
            # Validate input
            if not data or len(data.strip()) == 0:
                continue
            
            # Rate limiting check (basic implementation)
            if len(data) > 1000:
                await websocket.send_text("Message too long. Maximum 1000 characters.")
                continue
                
            await manager.broadcast(data)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
        await manager.broadcast("User left the chat.")
    except Exception as e:
        logging.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)

# Define the homepage route
@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

# Run the app
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

