<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Chat</title>
    <script src="{{ url_for('static', path='/main.js') }}"></script>
</head>
<body>
    <h1>WebSocket Chat</h1>
    <div id="messages"></div>
    <form>
        <input type="text" id="message">
        <button type="submit">Send</button>
    </form>
    <script>
        var socket = new WebSocket("ws://" + location.host + "/ws");
        socket.onmessage = function(event) {
            var messages = document.getElementById("messages");
            messages.innerHTML += "<p>" + event.data + "</p>";
        };
        document.querySelector("form").addEventListener("submit", function(event) {
            event.preventDefault();
            var message = document.getElementById("message").value;
            socket.send(message);
            document.getElementById("message").value = "";
        });
    </script>
</body>
</html>
