<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>WebSocket Chat with PyScript</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- PyScript CSS and JS -->
    <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css">
    <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
            background-color: #212121;
            color: #ececec;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .header {
            background-color: #2f2f2f;
            border-bottom: 1px solid #4a4a4a;
            padding: 16px 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
        }

        .header h1 {
            font-size: 18px;
            font-weight: 600;
            color: #ececec;
            margin: 0;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 768px;
            margin: 0 auto;
            width: 100%;
            height: 100%;
        }

        #messages {
            flex: 1;
            overflow-y: auto;
            padding: 24px;
            background-color: #212121;
            scroll-behavior: smooth;
        }

        .message {
            margin-bottom: 24px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            max-width: 100%;
        }

        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            background: linear-gradient(135deg, #10a37f, #1a7f64);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            color: white;
            flex-shrink: 0;
        }

        .message-content {
            flex: 1;
            min-width: 0;
        }

        .message-text {
            background-color: transparent;
            color: #ececec;
            padding: 0;
            border-radius: 8px;
            font-size: 16px;
            line-height: 1.6;
            word-wrap: break-word;
            white-space: pre-wrap;
        }

        .input-container {
            padding: 24px;
            background-color: #212121;
            border-top: 1px solid #4a4a4a;
        }

        .input-wrapper {
            max-width: 768px;
            margin: 0 auto;
            position: relative;
        }

        form {
            display: flex;
            align-items: flex-end;
            gap: 12px;
            background-color: #2f2f2f;
            border: 1px solid #4a4a4a;
            border-radius: 12px;
            padding: 12px;
            transition: border-color 0.2s ease;
        }

        form:focus-within {
            border-color: #10a37f;
            box-shadow: 0 0 0 2px rgba(16, 163, 127, 0.1);
        }

        #message {
            flex: 1;
            background: transparent;
            border: none;
            color: #ececec;
            font-size: 16px;
            line-height: 1.5;
            resize: none;
            outline: none;
            min-height: 24px;
            max-height: 120px;
            font-family: inherit;
        }

        #message::placeholder {
            color: #8e8ea0;
        }

        button {
            background-color: #10a37f;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 8px 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 60px;
        }

        button:hover:not(:disabled) {
            background-color: #0d8f6b;
        }

        button:disabled {
            background-color: #4a4a4a;
            cursor: not-allowed;
            opacity: 0.5;
        }

        /* Custom scrollbar */
        #messages::-webkit-scrollbar {
            width: 8px;
        }

        #messages::-webkit-scrollbar-track {
            background: #212121;
        }

        #messages::-webkit-scrollbar-thumb {
            background: #4a4a4a;
            border-radius: 4px;
        }

        #messages::-webkit-scrollbar-thumb:hover {
            background: #5a5a5a;
        }

        /* Connection status indicator */
        .connection-status {
            position: absolute;
            top: 50%;
            right: 24px;
            transform: translateY(-50%);
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            z-index: 1000;
            transition: all 0.3s ease;
        }

        .connection-status.connected {
            background-color: rgba(16, 163, 127, 0.1);
            color: #10a37f;
            border: 1px solid rgba(16, 163, 127, 0.2);
        }

        .connection-status.disconnected {
            background-color: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .header {
                padding: 12px 16px;
            }

            #messages {
                padding: 16px;
            }

            .input-container {
                padding: 16px;
            }

            .message {
                margin-bottom: 16px;
            }

            .message-text {
                font-size: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="connection-status" id="connection-status">Connecting...</div>

    <div class="header">
        <h1>WebSocket Chat</h1>
    </div>

    <div class="chat-container">
        <div id="messages"></div>

        <div class="input-container">
            <div class="input-wrapper">
                <form id="chat-form">
                    <textarea id="message" placeholder="Message WebSocket Chat..." autocomplete="off" rows="1"></textarea>
                    <button type="submit" id="send-button">Send</button>
                </form>
            </div>
        </div>
    </div>

    <py-script>
        import js
        from pyodide.ffi import create_proxy
        import time

        # Global variables
        socket = None
        message_counter = 0

        def get_websocket_url():
            """Get the appropriate WebSocket URL based on current protocol"""
            protocol = "wss://" if js.location.protocol == "https:" else "ws://"
            return f"{protocol}{js.location.host}/ws"

        def update_connection_status(status, message):
            """Update the connection status indicator"""
            status_element = js.document.getElementById("connection-status")
            status_element.textContent = message
            status_element.className = f"connection-status {status}"

        def create_message_element(text, is_user=False):
            """Create a message element with ChatGPT-style formatting"""
            global message_counter
            message_counter += 1

            # Create message container
            message_div = js.document.createElement("div")
            message_div.className = "message"

            # Create avatar
            avatar_div = js.document.createElement("div")
            avatar_div.className = "message-avatar"
            if is_user:
                avatar_div.textContent = "U"
                avatar_div.style.background = "linear-gradient(135deg, #3b82f6, #1d4ed8)"
            else:
                avatar_div.textContent = "C"
                avatar_div.style.background = "linear-gradient(135deg, #10a37f, #1a7f64)"

            # Create content container
            content_div = js.document.createElement("div")
            content_div.className = "message-content"

            # Create text element
            text_div = js.document.createElement("div")
            text_div.className = "message-text"
            text_div.textContent = text

            # Assemble the message
            content_div.appendChild(text_div)
            message_div.appendChild(avatar_div)
            message_div.appendChild(content_div)

            return message_div

        def on_message(event):
            """Handle incoming WebSocket messages"""
            messages_div = js.document.getElementById("messages")
            message_element = create_message_element(event.data, False)
            messages_div.appendChild(message_element)
            # Auto-scroll to bottom
            messages_div.scrollTop = messages_div.scrollHeight

        def auto_resize_textarea():
            """Auto-resize the textarea based on content"""
            textarea = js.document.getElementById("message")
            textarea.style.height = "auto"
            textarea.style.height = f"{textarea.scrollHeight}px"

        def on_form_submit(event):
            """Handle form submission"""
            event.preventDefault()
            message_input = js.document.getElementById("message")
            message = message_input.value.strip()

            if message and socket and socket.readyState == 1:  # WebSocket.OPEN
                # Add user message to chat
                messages_div = js.document.getElementById("messages")
                user_message = create_message_element(message, True)
                messages_div.appendChild(user_message)
                messages_div.scrollTop = messages_div.scrollHeight

                # Send message via WebSocket
                socket.send(message)
                message_input.value = ""
                auto_resize_textarea()

                # Update send button state
                send_button = js.document.getElementById("send-button")
                send_button.disabled = True
                js.setTimeout(create_proxy(lambda: setattr(send_button, 'disabled', False)), 100)

        def on_input(event):
            """Handle textarea input for auto-resize and send button state"""
            auto_resize_textarea()

            # Update send button state
            send_button = js.document.getElementById("send-button")
            message_input = js.document.getElementById("message")
            send_button.disabled = not message_input.value.strip()

        def on_keydown(event):
            """Handle keyboard shortcuts"""
            if event.key == "Enter" and not event.shiftKey:
                event.preventDefault()
                form = js.document.getElementById("chat-form")
                form.dispatchEvent(js.Event.new("submit"))

        def connect_websocket():
            """Initialize WebSocket connection"""
            global socket
            try:
                update_connection_status("disconnected", "Connecting...")
                socket = js.WebSocket.new(get_websocket_url())

                def on_open(event):
                    update_connection_status("connected", "Connected")
                    print("WebSocket connected")

                def on_close(event):
                    update_connection_status("disconnected", "Disconnected")
                    print("WebSocket disconnected")

                def on_error(event):
                    update_connection_status("disconnected", "Connection Error")
                    print("WebSocket error")

                socket.onmessage = create_proxy(on_message)
                socket.onopen = create_proxy(on_open)
                socket.onclose = create_proxy(on_close)
                socket.onerror = create_proxy(on_error)

            except Exception as e:
                update_connection_status("disconnected", "Connection Failed")
                print(f"Error connecting to WebSocket: {e}")

        # Initialize the application
        def init():
            """Initialize the chat application"""
            # Connect to WebSocket
            connect_websocket()

            # Set up form event listener
            form = js.document.getElementById("chat-form")
            form.addEventListener("submit", create_proxy(on_form_submit))

            # Set up textarea event listeners
            message_input = js.document.getElementById("message")
            message_input.addEventListener("input", create_proxy(on_input))
            message_input.addEventListener("keydown", create_proxy(on_keydown))

            # Initial button state
            send_button = js.document.getElementById("send-button")
            send_button.disabled = True

            # Focus on message input
            message_input.focus()

            # Add welcome message
            messages_div = js.document.getElementById("messages")
            welcome_message = create_message_element("Welcome to WebSocket Chat! Start typing to begin.", False)
            messages_div.appendChild(welcome_message)

        # Start the application
        init()
    </py-script>
</body>
</html>
