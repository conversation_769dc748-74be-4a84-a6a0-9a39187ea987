<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>WebSocket Chat with PyScript</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- PyScript CSS and JS -->
    <link rel="stylesheet" href="https://pyscript.net/releases/2024.1.1/core.css">
    <script type="module" src="https://pyscript.net/releases/2024.1.1/core.js"></script>

    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        #messages {
            border: 1px solid #ccc;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 20px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 5px;
            background-color: white;
            border-radius: 5px;
        }
        form {
            display: flex;
            gap: 10px;
        }
        #message {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        button {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>WebSocket Chat with PyScript</h1>
    <div id="messages"></div>
    <form id="chat-form">
        <input type="text" id="message" placeholder="Type your message here..." autocomplete="off">
        <button type="submit">Send</button>
    </form>

    <py-script>
        import js
        from pyodide.ffi import create_proxy

        # Global variables
        socket = None

        def get_websocket_url():
            """Get the appropriate WebSocket URL based on current protocol"""
            protocol = "wss://" if js.location.protocol == "https:" else "ws://"
            return f"{protocol}{js.location.host}/ws"

        def on_message(event):
            """Handle incoming WebSocket messages"""
            messages_div = js.document.getElementById("messages")
            message_element = js.document.createElement("div")
            message_element.className = "message"
            message_element.textContent = event.data
            messages_div.appendChild(message_element)
            # Auto-scroll to bottom
            messages_div.scrollTop = messages_div.scrollHeight

        def on_form_submit(event):
            """Handle form submission"""
            event.preventDefault()
            message_input = js.document.getElementById("message")
            message = message_input.value.strip()

            if message and socket and socket.readyState == 1:  # WebSocket.OPEN
                socket.send(message)
                message_input.value = ""

        def connect_websocket():
            """Initialize WebSocket connection"""
            global socket
            try:
                socket = js.WebSocket.new(get_websocket_url())
                socket.onmessage = create_proxy(on_message)
                socket.onopen = create_proxy(lambda event: print("WebSocket connected"))
                socket.onclose = create_proxy(lambda event: print("WebSocket disconnected"))
                socket.onerror = create_proxy(lambda event: print("WebSocket error"))
            except Exception as e:
                print(f"Error connecting to WebSocket: {e}")

        # Initialize the application
        def init():
            """Initialize the chat application"""
            # Connect to WebSocket
            connect_websocket()

            # Set up form event listener
            form = js.document.getElementById("chat-form")
            form.addEventListener("submit", create_proxy(on_form_submit))

            # Focus on message input
            js.document.getElementById("message").focus()

        # Start the application
        init()
    </py-script>
</body>
</html>
